import { reserveEquipmentForReservation } from "@/lib/availability";
import { calculateBookingPrice, validateBookingForCreation } from "@/lib/booking-validation";
import { appConfig } from "@/lib/env";
import { generateBookingQRCode, generateVerificationUrl } from "@/lib/qr-code";
import { supabase, supabaseAdmin } from "@/lib/supabase";
import { BookingFormData } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const bookingData: BookingFormData = await request.json();

		// Debug: Log the complete booking data
		console.log("=== BOOKING DEBUG START ===");
		console.log("Received booking data:", JSON.stringify(bookingData, null, 2));
		console.log("TimeSlotId:", bookingData.timeSlotId);
		console.log("TimeSlotId parts:", bookingData.timeSlotId.split("|"));
		console.log("ServiceId:", bookingData.serviceId);
		console.log("Participants:", bookingData.participants);
		console.log("Customer info:", bookingData.customerInfo);

		// Validate booking data
		console.log("Starting validation...");
		const validation = await validateBookingForCreation(bookingData);
		console.log("Validation result:", {
			isValid: validation.isValid,
			errors: validation.errors,
			warnings: validation.warnings,
		});

		if (!validation.isValid) {
			console.log("=== VALIDATION FAILED ===");
			console.log("Errors:", validation.errors);
			console.log("Warnings:", validation.warnings);
			console.log("=== BOOKING DEBUG END ===");
			return NextResponse.json(
				{
					error: "Validation failed",
					details: validation.errors,
					warnings: validation.warnings,
				},
				{ status: 400 }
			);
		}

		console.log("Validation passed, proceeding with booking creation...");

		// Calculate final pricing
		const priceCalculation = await calculateBookingPrice(bookingData.serviceId, bookingData.participants);

		// Create/update customer - customers table is now independent and self-contained
		// Use admin client for server-side operations
		const adminClient = supabaseAdmin || supabase; // Fallback to regular client if admin not available

		// Check if customer exists by email
		const { data: existingCustomer } = await adminClient
			.from("customers")
			.select("*")
			.eq("email", bookingData.customerInfo.email)
			.single();

		let customer;
		if (existingCustomer) {
			// Update existing customer
			const { data: updatedCustomer, error: updateError } = await adminClient
				.from("customers")
				.update({
					first_name: bookingData.customerInfo.firstName,
					last_name: bookingData.customerInfo.lastName,
					phone: bookingData.customerInfo.phone,
					emergency_contact_name: bookingData.customerInfo.emergencyContactName,
					emergency_contact_phone: bookingData.customerInfo.emergencyContactPhone,
				})
				.eq("email", bookingData.customerInfo.email)
				.select()
				.single();

			if (updateError) {
				console.error("Error updating customer:", updateError);
				return NextResponse.json({ error: "Failed to update customer" }, { status: 500 });
			}
			customer = updatedCustomer;
		} else {
			// Create new customer directly
			const customerId = generateUUID();
			console.log("Generated customer ID:", customerId);
			console.log("Creating new independent customer record...");
			console.log("Customer approach: direct customer table insert");

			// Create customer directly with all needed fields
			const { data: newCustomer, error: createError } = await adminClient
				.from("customers")
				.insert({
					id: customerId,
					email: bookingData.customerInfo.email,
					first_name: bookingData.customerInfo.firstName,
					last_name: bookingData.customerInfo.lastName,
					phone: bookingData.customerInfo.phone,
					emergency_contact_name: bookingData.customerInfo.emergencyContactName,
					emergency_contact_phone: bookingData.customerInfo.emergencyContactPhone,
				})
				.select()
				.single();

			if (createError) {
				console.error("Error creating customer:", createError);
				return NextResponse.json({ error: "Failed to create customer record" }, { status: 500 });
			}
			customer = newCustomer;
		}

		// Parse dynamic time slot ID to get start and end times
		const timeSlotParts = bookingData.timeSlotId.split("|");
		const date = timeSlotParts[1];
		const time = timeSlotParts[2];

		// Get service duration to calculate end time
		const { data: serviceDetails } = await supabase
			.from("services")
			.select("duration_minutes")
			.eq("id", bookingData.serviceId)
			.single();

		const startTime = new Date(`${date}T${time}:00`);
		const endTime = new Date(startTime);
		endTime.setMinutes(endTime.getMinutes() + (serviceDetails?.duration_minutes || 60));

		// Create reservation
		const reservationNumber = generateReservationNumber();

		// Generate QR code data URL with booking information
		const verificationUrl = generateVerificationUrl(reservationNumber, appConfig.url);
		const qrCodeData = await generateBookingQRCode({
			reservationId: reservationNumber,
			reservationNumber: reservationNumber,
			customerName: `${customer.first_name} ${customer.last_name}`,
			serviceName: serviceDetails?.name || "Service",
			date: date,
			time: time,
			participants: bookingData.participants.length,
			totalAmount: priceCalculation.total,
			verificationUrl: verificationUrl,
		});

		const reservationData = {
			service_id: bookingData.serviceId,
			customer_id: customer.id,
			reservation_number: reservationNumber,
			start_time: startTime.toISOString(),
			end_time: endTime.toISOString(),
			participant_count: bookingData.participants.length,
			total_amount: priceCalculation.total,
			currency: "EUR",
			status: "pending" as const,
			booking_source: "website",
			requires_confirmation: false,
			special_requests: bookingData.specialRequests || null,
			qr_code: qrCodeData, // Real QR code data URL
		};

		const { data: reservation, error: reservationError } = await adminClient
			.from("reservations")
			.insert(reservationData)
			.select()
			.single();

		if (reservationError) {
			console.error("Error creating reservation:", reservationError);
			return NextResponse.json({ error: "Failed to create reservation" }, { status: 500 });
		}

		// Reserve equipment
		console.log("Attempting to reserve equipment for reservation:", reservation.id);
		const equipmentReserved = await reserveEquipmentForReservation(
			bookingData.serviceId,
			reservation.id,
			bookingData.participants.length,
			startTime.toISOString(),
			endTime.toISOString()
		);
		console.log("Equipment reservation result:", equipmentReserved);

		if (!equipmentReserved) {
			console.error("Failed to reserve equipment");
			// Rollback reservation
			await adminClient.from("reservations").delete().eq("id", reservation.id);
			return NextResponse.json({ error: "Failed to reserve equipment" }, { status: 500 });
		}

		// Note: With dynamic availability, we don't need to update time slot status
		// Availability is calculated in real-time based on existing reservations

		// Create notification for admin about new booking
		try {
			const { createNotification, createBookingConfirmationNotification } = await import("@/lib/notifications");

			// Get service and customer details for notification
			const { data: service } = await adminClient
				.from("services")
				.select("name")
				.eq("id", bookingData.serviceId)
				.single();

			if (service) {
				const customerName = `${customer.first_name} ${customer.last_name}`;
				const serviceName = service.name;
				const date = new Date(startTime).toLocaleDateString("fr-FR");

				// Get admin users to notify
				const { data: adminProfiles } = await supabase.from("profiles").select("id").eq("role", "admin");

				// Create notification for each admin
				if (adminProfiles) {
					const notificationTemplate = createBookingConfirmationNotification(
						customerName,
						serviceName,
						date,
						reservation.id
					);

					for (const admin of adminProfiles) {
						await createNotification(admin.id, notificationTemplate, reservation.id);
					}
				}
			}
		} catch (notificationError) {
			console.error("Error creating booking notification:", notificationError);
			// Don't fail the booking if notification fails
		}

		// Send booking confirmation email
		try {
			const { sendBookingConfirmationEmail, generateBookingConfirmationPDF } = await import(
				"@/lib/email-service"
			);

			// Generate PDF attachment
			const pdfData = await generateBookingConfirmationPDF({
				reservationNumber: reservation.reservation_number,
				customerName: `${customer.first_name} ${customer.last_name}`,
				email: customer.email,
				phone: customer.phone || "",
				serviceName: serviceDetails?.name || "Service",
				date: date,
				time: time,
				participants: bookingData.participants.length,
				totalAmount: priceCalculation.total,
				qrCodeData: {
					reservationId: reservation.id,
					reservationNumber: reservation.reservation_number,
					customerName: `${customer.first_name} ${customer.last_name}`,
					serviceName: serviceDetails?.name || "Service",
					date: date,
					time: time,
					participants: bookingData.participants.length,
					totalAmount: priceCalculation.total,
					verificationUrl: verificationUrl,
				},
				specialRequests: bookingData.specialRequests,
			});

			// Convert PDF blob to buffer
			const pdfBuffer = Buffer.from(await pdfData.arrayBuffer());

			// Send email with PDF attachment
			const emailResult = await sendBookingConfirmationEmail(
				customer.email,
				`${customer.first_name} ${customer.last_name}`,
				{
					reservationNumber: reservation.reservation_number,
					serviceName: serviceDetails?.name || "Service",
					date: date,
					time: time,
					participants: bookingData.participants.length,
					totalAmount: priceCalculation.total,
					specialRequests: bookingData.specialRequests,
				},
				pdfBuffer
			);

			if (!emailResult.success) {
				console.error("Error sending confirmation email:", emailResult.error);
				// Don't fail the booking if email fails
			} else {
				console.log("Confirmation email sent successfully:", emailResult.messageId);
			}
		} catch (emailError) {
			console.error("Error sending booking confirmation email:", emailError);
			// Don't fail the booking if email fails
		}

		// Return success response
		console.log("=== BOOKING CREATION SUCCESS ===");
		console.log("Reservation created with ID:", reservation.id);
		console.log("Returning response data:", {
			reservationId: reservation.id,
			qrCode: reservation.qr_code,
			totalAmount: priceCalculation.total,
			status: "pending",
		});

		return NextResponse.json({
			success: true,
			data: {
				reservationId: reservation.id,
				qrCode: reservation.qr_code,
				totalAmount: priceCalculation.total,
				status: "pending",
				message: "Réservation créée avec succès",
			},
			warnings: validation.warnings,
		});
	} catch (error) {
		console.error("Error creating booking:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}

export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const customerId = searchParams.get("customer_id");
		const status = searchParams.get("status");
		const limit = parseInt(searchParams.get("limit") || "10");

		if (!supabaseAdmin) {
			return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
		}

		let query = supabaseAdmin
			.from("reservations")
			.select(
				`
        *,
        service:services(name, duration_minutes),
        customer:customers(
          id,
          emergency_contact_name,
          emergency_contact_phone,
          profile:profiles!customers_id_fkey(
            first_name,
            last_name,
            email,
            phone
          )
        )
      `
			)
			.order("created_at", { ascending: false })
			.limit(limit);

		if (customerId) {
			query = query.eq("customer_id", customerId);
		}

		if (status) {
			query = query.eq("status", status);
		}

		const { data: reservations, error } = await query;

		if (error) {
			console.error("Error fetching reservations:", error);
			return NextResponse.json({ error: "Failed to fetch reservations" }, { status: 500 });
		}

		return NextResponse.json({
			success: true,
			data: reservations,
		});
	} catch (error) {
		console.error("Error in GET /api/bookings:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}

// Utility function to generate reservation number
function generateReservationNumber(): string {
	const timestamp = Date.now().toString(36);
	const random = Math.random().toString(36).substring(2, 8);
	return `RES-${timestamp}-${random}`.toUpperCase();
}

// QR code generation is now handled by the generateBookingQRCode function from @/lib/qr-code

// Utility function to generate UUID
function generateUUID(): string {
	// Simple UUID v4 generation
	return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
		const r = (Math.random() * 16) | 0;
		const v = c === "x" ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
}
